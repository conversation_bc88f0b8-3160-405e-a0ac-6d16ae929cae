/**
 * 使用示例文件
 * 展示如何使用 PromptShowcase 和 ComparisonShowcase 组件
 */

import { PromptShowcase, ComparisonShowcase } from "./index";
import { PromptShowcaseProps, ComparisonShowcaseProps } from "@/types/blocks/showcase";

// PromptShowcase 使用示例 - 基础网格布局
export function PromptShowcaseExample() {
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "AI Prompt 精选集",
      description: "精心挑选的高质量 AI Prompt，一键复制即可使用",
      items: [
        {
          title: "专业文案写作",
          description: "适用于营销文案、产品描述等商业写作场景",
          prompt: "请以专业营销文案的角度，为[产品名称]撰写一段吸引人的产品描述。要求：\n1. 突出产品核心优势\n2. 使用情感化语言\n3. 包含行动号召\n4. 字数控制在100-150字",
          category: "文案写作",
          tags: ["营销", "产品描述", "商业文案"],
          images: [
            {
              src: "/images/showcase/writing-prompt-1.jpg",
              alt: "文案写作示例1"
            },
            {
              src: "/images/showcase/writing-prompt-2.jpg",
              alt: "文案写作示例2"
            },
            {
              src: "/images/showcase/writing-prompt-3.jpg",
              alt: "文案写作示例3"
            }
          ]
        },
        {
          title: "代码优化建议",
          description: "帮助开发者优化代码质量和性能",
          prompt: "请分析以下代码并提供优化建议：\n\n[粘贴代码]\n\n请从以下角度分析：\n1. 代码可读性\n2. 性能优化\n3. 最佳实践\n4. 潜在bug\n5. 重构建议",
          category: "编程",
          tags: ["代码审查", "性能优化", "重构"],
          image: {
            src: "/images/showcase/code-review.jpg",
            alt: "代码优化示例"
          }
        },
        {
          title: "创意头脑风暴",
          description: "激发创意思维，产生新颖想法",
          prompt: "我需要为[项目/产品]进行创意头脑风暴。请帮我：\n1. 从5个不同角度分析这个主题\n2. 提供10个创新想法\n3. 每个想法包含简短说明\n4. 按可行性排序\n\n主题：[具体描述]",
          category: "创意",
          tags: ["头脑风暴", "创新", "策划"],
          image: {
            src: "/images/showcase/brainstorm.jpg",
            alt: "创意头脑风暴示例"
          }
        }
      ]
    }
  };

  return <PromptShowcase {...promptShowcaseData} />;
}

// PromptShowcase 轮播示例
export function PromptShowcaseCarouselExample() {
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "AI Prompt 轮播展示",
      description: "自动轮播的 AI Prompt 集合，支持手动控制",
      items: [
        {
          title: "专业文案写作",
          description: "适用于营销文案、产品描述等商业写作场景",
          prompt: "请以专业营销文案的角度，为[产品名称]撰写一段吸引人的产品描述。要求：\n1. 突出产品核心优势\n2. 使用情感化语言\n3. 包含行动号召\n4. 字数控制在100-150字",
          category: "文案写作",
          tags: ["营销", "产品描述", "商业文案"],
          image: {
            src: "/images/showcase/writing-prompt.jpg",
            alt: "文案写作示例"
          }
        },
        {
          title: "代码优化建议",
          description: "帮助开发者优化代码质量和性能",
          prompt: "请分析以下代码并提供优化建议：\n\n[粘贴代码]\n\n请从以下角度分析：\n1. 代码可读性\n2. 性能优化\n3. 最佳实践\n4. 潜在bug\n5. 重构建议",
          category: "编程",
          tags: ["代码审查", "性能优化", "重构"],
          image: {
            src: "/images/showcase/code-review.jpg",
            alt: "代码优化示例"
          }
        },
        {
          title: "创意头脑风暴",
          description: "激发创意思维，产生新颖想法",
          prompt: "我需要为[项目/产品]进行创意头脑风暴。请帮我：\n1. 从5个不同角度分析这个主题\n2. 提供10个创新想法\n3. 每个想法包含简短说明\n4. 按可行性排序\n\n主题：[具体描述]",
          category: "创意",
          tags: ["头脑风暴", "创新", "策划"],
          image: {
            src: "/images/showcase/brainstorm.jpg",
            alt: "创意头脑风暴示例"
          }
        }
      ]
    },
    cardWidth: '50%',
    autoplayDelay: 5000
  };

  return <PromptShowcase {...promptShowcaseData} />;
}

// PromptShowcase 水平布局示例
export function PromptShowcaseHorizontalExample() {
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "AI Prompt 水平布局",
      description: "图片与文字水平排列的展示方式",
      items: [
        {
          title: "专业文案写作",
          description: "适用于营销文案、产品描述等商业写作场景",
          prompt: "请以专业营销文案的角度，为[产品名称]撰写一段吸引人的产品描述。要求：\n1. 突出产品核心优势\n2. 使用情感化语言\n3. 包含行动号召\n4. 字数控制在100-150字",
          category: "文案写作",
          tags: ["营销", "产品描述", "商业文案"],
          image: {
            src: "/images/showcase/writing-prompt.jpg",
            alt: "文案写作示例"
          }
        },
        {
          title: "代码优化建议",
          description: "帮助开发者优化代码质量和性能",
          prompt: "请分析以下代码并提供优化建议：\n\n[粘贴代码]\n\n请从以下角度分析：\n1. 代码可读性\n2. 性能优化\n3. 最佳实践\n4. 潜在bug\n5. 重构建议",
          category: "编程",
          tags: ["代码审查", "性能优化", "重构"],
          image: {
            src: "/images/showcase/code-review.jpg",
            alt: "代码优化示例"
          }
        }
      ]
    },
    layout: 'horizontal',
    imagePosition: 'left'
  };

  return <PromptShowcase {...promptShowcaseData} />;
}

// ComparisonShowcase 使用示例 - 基础网格布局
export function ComparisonShowcaseExample() {
  const comparisonShowcaseData: ComparisonShowcaseProps = {
    section: {
      title: "AI 图像处理效果展示",
      description: "通过前后对比，展示 AI 图像处理的强大能力",
      items: [
        {
          title: "人像美化处理",
          description: "AI 智能美化，保持自然效果的同时提升照片质量",
          prompt: "Please enhance this portrait photo with natural beauty effects. Focus on:\n- Skin smoothing while maintaining texture\n- Eye brightening\n- Color correction\n- Overall lighting improvement\nKeep the result natural and avoid over-processing.",
          beforeImage: {
            src: "/images/showcase/portrait-before.jpg",
            alt: "处理前的人像照片"
          },
          afterImage: {
            src: "/images/showcase/portrait-after.jpg",
            alt: "AI美化后的人像照片"
          },
          category: "人像处理",
          tags: ["美颜", "人像", "自然效果"]
        },
        {
          title: "风景照片增强",
          description: "提升风景照片的色彩饱和度和清晰度",
          prompt: "Enhance this landscape photo with the following improvements:\n- Increase color vibrancy and saturation\n- Improve sky contrast\n- Enhance details and sharpness\n- Adjust exposure for better dynamic range\n- Maintain realistic color tones",
          beforeImage: {
            src: "/images/showcase/landscape-before.jpg",
            alt: "原始风景照片"
          },
          afterImage: {
            src: "/images/showcase/landscape-after.jpg",
            alt: "增强后的风景照片"
          },
          category: "风景摄影",
          tags: ["色彩增强", "风景", "HDR效果"]
        }
      ]
    }
  };

  return <ComparisonShowcase {...comparisonShowcaseData} />;
}

// ComparisonShowcase 轮播示例
export function ComparisonShowcaseCarouselExample() {
  const comparisonShowcaseData: ComparisonShowcaseProps = {
    section: {
      title: "AI 图像处理轮播展示",
      description: "自动轮播的图像处理效果对比",
      items: [
        {
          title: "人像美化处理",
          description: "AI 智能美化，保持自然效果的同时提升照片质量",
          prompt: "Please enhance this portrait photo with natural beauty effects. Focus on:\n- Skin smoothing while maintaining texture\n- Eye brightening\n- Color correction\n- Overall lighting improvement\nKeep the result natural and avoid over-processing.",
          beforeImage: {
            src: "/images/showcase/portrait-before.jpg",
            alt: "处理前的人像照片"
          },
          afterImage: {
            src: "/images/showcase/portrait-after.jpg",
            alt: "AI美化后的人像照片"
          },
          category: "人像处理",
          tags: ["美颜", "人像", "自然效果"]
        },
        {
          title: "风景照片增强",
          description: "提升风景照片的色彩饱和度和清晰度",
          prompt: "Enhance this landscape photo with the following improvements:\n- Increase color vibrancy and saturation\n- Improve sky contrast\n- Enhance details and sharpness\n- Adjust exposure for better dynamic range\n- Maintain realistic color tones",
          beforeImage: {
            src: "/images/showcase/landscape-before.jpg",
            alt: "原始风景照片"
          },
          afterImage: {
            src: "/images/showcase/landscape-after.jpg",
            alt: "增强后的风景照片"
          },
          category: "风景摄影",
          tags: ["色彩增强", "风景", "HDR效果"]
        },
        {
          title: "艺术风格转换",
          description: "将普通照片转换为艺术风格作品",
          prompt: "Transform this image into an artistic masterpiece with:\n- Oil painting style effects\n- Enhanced color palette\n- Artistic brush strokes\n- Dramatic lighting\n- Maintain subject recognition while adding artistic flair",
          beforeImage: {
            src: "/images/showcase/art-before.jpg",
            alt: "原始照片"
          },
          afterImage: {
            src: "/images/showcase/art-after.jpg",
            alt: "艺术风格转换后"
          },
          category: "艺术转换",
          tags: ["艺术风格", "油画", "创意"]
        }
      ]
    },
    cardWidth: '100%',
    autoplayDelay: 6000
  };

  return <ComparisonShowcase {...comparisonShowcaseData} />;
}

// 组合使用示例 - 基础版本
export function ShowcasePageExample() {
  return (
    <div className="space-y-16">
      <PromptShowcaseExample />
      <ComparisonShowcaseExample />
    </div>
  );
}

// 组合使用示例 - 轮播版本
export function ShowcasePageCarouselExample() {
  return (
    <div className="space-y-16">
      <PromptShowcaseCarouselExample />
      <ComparisonShowcaseCarouselExample />
    </div>
  );
}

// 组合使用示例 - 混合布局
export function ShowcasePageMixedExample() {
  return (
    <div className="space-y-16">
      {/* 水平布局的 Prompt Showcase */}
      <PromptShowcaseHorizontalExample />

      {/* 轮播的 Comparison Showcase */}
      <ComparisonShowcaseCarouselExample />

      {/* 基础网格的 Prompt Showcase */}
      <PromptShowcaseExample />
    </div>
  );
}

// 完整功能展示示例
export function ShowcaseFullFeaturesExample() {
  return (
    <div className="space-y-24">
      {/* 标题区域 */}
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Showcase 组件功能展示</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          展示 PromptShowcase 和 ComparisonShowcase 组件的所有功能，包括轮播、布局选项和一键复制等特性
        </p>
      </div>

      {/* 基础网格布局 */}
      <section>
        <h2 className="text-2xl font-semibold mb-8 text-center">基础网格布局</h2>
        <PromptShowcaseExample />
      </section>

      {/* 轮播功能 */}
      <section>
        <h2 className="text-2xl font-semibold mb-8 text-center">轮播功能展示</h2>
        <PromptShowcaseCarouselExample />
      </section>

      {/* 水平布局 */}
      <section>
        <h2 className="text-2xl font-semibold mb-8 text-center">水平布局展示</h2>
        <PromptShowcaseHorizontalExample />
      </section>

      {/* 对比展示 - 基础 */}
      <section>
        <h2 className="text-2xl font-semibold mb-8 text-center">图像对比展示</h2>
        <ComparisonShowcaseExample />
      </section>

      {/* 对比展示 - 轮播 */}
      <section>
        <h2 className="text-2xl font-semibold mb-8 text-center">图像对比轮播</h2>
        <ComparisonShowcaseCarouselExample />
      </section>
    </div>
  );
}
