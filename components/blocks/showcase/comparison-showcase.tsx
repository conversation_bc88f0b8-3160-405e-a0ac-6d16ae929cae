"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect, useCallback } from "react";
import { Copy, Check, ArrowRight } from "lucide-react";
import Image from "next/image";
import { ComparisonShowcaseProps } from "@/types/blocks/showcase";
import useEmblaCarousel from 'embla-carousel-react';
import AutoScroll from 'embla-carousel-auto-scroll';

export default function ComparisonShowcase({
  section,
  cardWidth = 'auto',
  autoplayDelay = 0
}: ComparisonShowcaseProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  if (section.disabled) {
    return null;
  }

  // 对比图片轮播组件
  const ComparisonImageCarousel = ({
    beforeImages,
    afterImages,
    beforeAlt,
    afterAlt
  }: {
    beforeImages: { src: string; alt: string }[],
    afterImages: { src: string; alt: string }[],
    beforeAlt: string,
    afterAlt: string
  }) => {
    const [emblaRef, emblaApi] = useEmblaCarousel(
      { loop: true },
      autoplayDelay > 0 ? [AutoScroll({ speed: 1, startDelay: autoplayDelay })] : []
    );

    const [currentIndex, setCurrentIndex] = useState(0);

    const onSelect = useCallback(() => {
      if (!emblaApi) return;
      setCurrentIndex(emblaApi.selectedScrollSnap());
    }, [emblaApi]);

    useEffect(() => {
      if (!emblaApi) return;
      onSelect();
      emblaApi.on('select', onSelect);
      emblaApi.on('reInit', onSelect);
    }, [emblaApi, onSelect]);

    if (beforeImages.length === 1 && afterImages.length === 1) {
      // 单张图片对比，不需要轮播
      return (
        <div className="space-y-4">
          {/* 移动端：垂直布局 */}
          <div className="block md:hidden space-y-4">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">处理前</p>
              <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                <Image
                  src={beforeImages[0].src}
                  alt={beforeImages[0].alt}
                  width={600}
                  height={400}
                  className="w-full h-auto object-contain transition-transform duration-300 hover:scale-105"
                />
              </div>
            </div>

            <div className="flex justify-center">
              <ArrowRight className="w-6 h-6 text-muted-foreground rotate-90" />
            </div>

            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">处理后</p>
              <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                <Image
                  src={afterImages[0].src}
                  alt={afterImages[0].alt}
                  width={600}
                  height={400}
                  className="w-full h-auto object-contain transition-transform duration-300 hover:scale-105"
                />
              </div>
            </div>
          </div>

          {/* 桌面端：水平布局 */}
          <div className="hidden md:flex items-center gap-4">
            <div className="flex-1 space-y-2">
              <p className="text-sm font-medium text-muted-foreground">处理前</p>
              <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                <Image
                  src={beforeImages[0].src}
                  alt={beforeImages[0].alt}
                  width={600}
                  height={400}
                  className="w-full h-auto object-contain transition-transform duration-300 hover:scale-105"
                />
              </div>
            </div>

            <div className="flex-shrink-0">
              <ArrowRight className="w-6 h-6 text-muted-foreground" />
            </div>

            <div className="flex-1 space-y-2">
              <p className="text-sm font-medium text-muted-foreground">处理后</p>
              <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                <Image
                  src={afterImages[0].src}
                  alt={afterImages[0].alt}
                  width={600}
                  height={400}
                  className="w-full h-auto object-contain transition-transform duration-300 hover:scale-105"
                />
              </div>
            </div>
          </div>
        </div>
      );
    }

    // 多张图片对比，使用轮播
    return (
      <div className="space-y-4">
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex">
            {beforeImages.map((_, index) => (
              <div key={index} className="flex-[0_0_100%] min-w-0">
                {/* 移动端：垂直布局 */}
                <div className="block md:hidden space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">处理前</p>
                    <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                      <Image
                        src={beforeImages[index].src}
                        alt={beforeImages[index].alt}
                        width={600}
                        height={400}
                        className="w-full h-auto object-contain"
                      />
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <ArrowRight className="w-6 h-6 text-muted-foreground rotate-90" />
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">处理后</p>
                    <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                      <Image
                        src={afterImages[index].src}
                        alt={afterImages[index].alt}
                        width={600}
                        height={400}
                        className="w-full h-auto object-contain"
                      />
                    </div>
                  </div>
                </div>

                {/* 桌面端：水平布局 */}
                <div className="hidden md:flex items-center gap-4">
                  <div className="flex-1 space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">处理前</p>
                    <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                      <Image
                        src={beforeImages[index].src}
                        alt={beforeImages[index].alt}
                        width={600}
                        height={400}
                        className="w-full h-auto object-contain"
                      />
                    </div>
                  </div>

                  <div className="flex-shrink-0">
                    <ArrowRight className="w-6 h-6 text-muted-foreground" />
                  </div>

                  <div className="flex-1 space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">处理后</p>
                    <div className="relative w-full overflow-hidden rounded-lg border border-border/50">
                      <Image
                        src={afterImages[index].src}
                        alt={afterImages[index].alt}
                        width={600}
                        height={400}
                        className="w-full h-auto object-contain"
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 轮播指示器 */}
        {beforeImages.length > 1 && (
          <div className="flex justify-center gap-1">
            {beforeImages.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentIndex ? 'bg-primary' : 'bg-muted-foreground/30'
                }`}
                onClick={() => emblaApi?.scrollTo(index)}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  const handleCopyPrompt = async (prompt: string, index: number) => {
    try {
      await navigator.clipboard.writeText(prompt);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
    }
  };

  // 渲染单个卡片的函数
  const renderCard = (item: any, index: number) => {
    // 确定使用的图片数组
    const beforeImages = item.beforeImages || (item.beforeImage ? [item.beforeImage] : []);
    const afterImages = item.afterImages || (item.afterImage ? [item.afterImage] : []);

    // 计算卡片宽度样式
    const getCardWidthClass = () => {
      switch (cardWidth) {
        case '50%':
          return 'w-full lg:w-1/2';
        case '100%':
          return 'w-full';
        default:
          return 'max-w-4xl mx-auto w-full';
      }
    };

    return (
      <Card
        key={index}
        className={`overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col ${getCardWidthClass()}`}
      >
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between gap-2">
            <CardTitle className="text-xl font-semibold line-clamp-2 flex-1">
              {item.title}
            </CardTitle>
            {item.category && (
              <Badge variant="outline" className="shrink-0">
                {item.category}
              </Badge>
            )}
          </div>
          {item.description && (
            <p className="text-sm text-muted-foreground line-clamp-2 mt-2">
              {item.description}
            </p>
          )}
        </CardHeader>

        <CardContent className="flex-1 flex flex-col space-y-6">
          {/* 图片对比区域 */}
          {beforeImages.length > 0 && afterImages.length > 0 && (
            <ComparisonImageCarousel
              beforeImages={beforeImages}
              afterImages={afterImages}
              beforeAlt={item.title + " - 处理前"}
              afterAlt={item.title + " - 处理后"}
            />
          )}

          {/* Prompt 内容区域 */}
          <div className="space-y-3">
            <p className="text-sm font-medium text-foreground">使用的 Prompt</p>
            <div className="bg-muted/50 rounded-lg p-4 border border-border/50">
              <p className="text-sm font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                {item.prompt}
              </p>
            </div>
          </div>

          {/* 标签区域 */}
          {item.tags && item.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {item.tags.slice(0, 4).map((tag: string, tagIndex: number) => (
                <Badge
                  key={tagIndex}
                  variant="secondary"
                  className="text-xs px-2 py-1"
                >
                  {tag}
                </Badge>
              ))}
              {item.tags.length > 4 && (
                <Badge variant="secondary" className="text-xs px-2 py-1">
                  +{item.tags.length - 4}
                </Badge>
              )}
            </div>
          )}

          {/* 复制按钮 */}
          <Button
            onClick={() => handleCopyPrompt(item.prompt, index)}
            variant="outline"
            size="sm"
            className="w-full transition-all duration-200 hover:bg-primary hover:text-primary-foreground"
          >
            {copiedIndex === index ? (
              <>
                <Check className="w-4 h-4 mr-2" />
                已复制
              </>
            ) : (
              <>
                <Copy className="w-4 h-4 mr-2" />
                复制 Prompt
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  };

  return (
    <section className="container py-16">
      <div className="mx-auto mb-12 text-center">
        <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
          {section.title}
        </h2>
        <p className="mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
          {section.description}
        </p>
      </div>

      {/* 网格布局 */}
      <div className={`grid gap-8 ${
        cardWidth === '50%'
          ? 'grid-cols-1 lg:grid-cols-2'
          : cardWidth === '100%'
          ? 'grid-cols-1'
          : 'grid-cols-1 max-w-none'
      }`}>
        {section.items?.map((item, index) => renderCard(item, index))}
      </div>
    </section>
  );
}
