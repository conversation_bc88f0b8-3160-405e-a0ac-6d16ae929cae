"use client";

import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect, useCallback } from "react";
import { Copy, Check, ChevronDown, ChevronUp } from "lucide-react";
import Image from "next/image";
import { PromptShowcaseProps } from "@/types/blocks/showcase";
import useEmblaCarousel from 'embla-carousel-react';
import AutoScroll from 'embla-carousel-auto-scroll';

export default function PromptShowcase({
  section,
  layout = 'vertical',
  imagePosition = 'left',
  cardWidth = 'auto',
  autoplayDelay = 0
}: PromptShowcaseProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [expandedPrompts, setExpandedPrompts] = useState<Set<number>>(new Set());

  if (section.disabled) {
    return null;
  }

  // 卡片内图片轮播组件
  const ImageCarousel = ({ images, alt, category }: {
    images: { src: string; alt?: string }[],
    alt: string,
    category?: string
  }) => {
    const [emblaRef, emblaApi] = useEmblaCarousel(
      { loop: true },
      autoplayDelay > 0 ? [AutoScroll({ speed: 1, startDelay: autoplayDelay })] : []
    );

    const [currentIndex, setCurrentIndex] = useState(0);

    const onSelect = useCallback(() => {
      if (!emblaApi) return;
      setCurrentIndex(emblaApi.selectedScrollSnap());
    }, [emblaApi]);

    useEffect(() => {
      if (!emblaApi) return;
      onSelect();
      emblaApi.on('select', onSelect);
      emblaApi.on('reInit', onSelect);
    }, [emblaApi, onSelect]);

    if (images.length === 1) {
      // 单张图片，不需要轮播
      return (
        <div className="relative aspect-[4/3] w-full overflow-hidden">
          <Image
            src={images[0].src}
            alt={images[0].alt || alt}
            fill
            className="object-cover transition-transform duration-300 hover:scale-105"
          />
          {category && (
            <div className="absolute top-3 right-3">
              <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
                {category}
              </Badge>
            </div>
          )}
        </div>
      );
    }

    // 多张图片，使用轮播
    return (
      <div className="relative aspect-[4/3] w-full overflow-hidden">
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex">
            {images.map((img, index) => (
              <div key={index} className="flex-[0_0_100%] min-w-0">
                <Image
                  src={img.src}
                  alt={img.alt || `${alt} ${index + 1}`}
                  width={600}
                  height={450}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* 轮播指示器 */}
        <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex gap-1">
          {images.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-all ${
                index === currentIndex ? 'bg-white' : 'bg-white/50'
              }`}
              onClick={() => emblaApi?.scrollTo(index)}
            />
          ))}
        </div>

        {category && (
          <div className="absolute top-3 right-3">
            <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
              {category}
            </Badge>
          </div>
        )}
      </div>
    );
  };

  const handleCopyPrompt = async (prompt: string, index: number) => {
    try {
      await navigator.clipboard.writeText(prompt);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
    }
  };

  const togglePrompt = (index: number) => {
    const newExpanded = new Set(expandedPrompts);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedPrompts(newExpanded);
  };

  // 渲染单个卡片的函数
  const renderCard = (item: any, index: number) => {
    // 确定使用的图片数组
    const images = item.images || (item.image ? [item.image] : []);

    // 计算卡片宽度样式
    const getCardWidthClass = () => {
      switch (cardWidth) {
        case '50%':
          return 'w-full md:w-1/2';
        case '100%':
          return 'w-full';
        default:
          return '';
      }
    };

    if (layout === 'horizontal') {
      // 水平布局
      return (
        <Card
          key={index}
          className={`overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full ${getCardWidthClass()}`}
        >
          <div className={`flex flex-col md:flex-row ${imagePosition === 'right' ? 'md:flex-row-reverse' : ''} h-full`}>
            {/* 图片区域 */}
            {images.length > 0 && (
              <div className="w-full md:w-1/2">
                <ImageCarousel
                  images={images}
                  alt={item.title}
                  category={item.category}
                />
              </div>
            )}

            {/* 内容区域 */}
            <div className="flex-1 flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold line-clamp-2">
                  {item.title}
                </CardTitle>
                {item.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                    {item.description}
                  </p>
                )}
              </CardHeader>

              <CardContent className="flex-1 flex flex-col space-y-3">
                {/* 标签区域 */}
                {item.tags && item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {item.tags.slice(0, 3).map((tag: string, tagIndex: number) => (
                      <Badge
                        key={tagIndex}
                        variant="secondary"
                        className="text-xs px-2 py-0.5"
                      >
                        {tag}
                      </Badge>
                    ))}
                    {item.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs px-2 py-0.5">
                        +{item.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Prompt 展开/收起区域 */}
                <div className="flex-1">
                  {expandedPrompts.has(index) && (
                    <div className="mb-3">
                      <div className="bg-muted/50 rounded-lg p-3 border border-border/50">
                        <p className="text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                          {item.prompt}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* 按钮区域 */}
                <div className="space-y-2">
                  <Button
                    onClick={() => togglePrompt(index)}
                    variant="outline"
                    size="sm"
                    className="w-full transition-all duration-200"
                  >
                    {expandedPrompts.has(index) ? (
                      <>
                        <ChevronUp className="w-4 h-4 mr-2" />
                        隐藏 Prompt
                      </>
                    ) : (
                      <>
                        <ChevronDown className="w-4 h-4 mr-2" />
                        显示 Prompt
                      </>
                    )}
                  </Button>

                  {expandedPrompts.has(index) && (
                    <Button
                      onClick={() => handleCopyPrompt(item.prompt, index)}
                      variant="default"
                      size="sm"
                      className="w-full transition-all duration-200"
                    >
                      {copiedIndex === index ? (
                        <>
                          <Check className="w-4 h-4 mr-2" />
                          已复制
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 mr-2" />
                          复制 Prompt
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </div>
          </div>
        </Card>
      );
    } else {
      // 垂直布局（原有布局）
      return (
        <Card
          key={index}
          className={`overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full ${getCardWidthClass()}`}
        >
          {/* 主要图片区域 */}
          {images.length > 0 && (
            <ImageCarousel
              images={images}
              alt={item.title}
              category={item.category}
            />
          )}

          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold line-clamp-2">
              {item.title}
            </CardTitle>
            {item.description && (
              <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                {item.description}
              </p>
            )}
          </CardHeader>

          <CardContent className="flex-1 flex flex-col space-y-3">
            {/* 标签区域 */}
            {item.tags && item.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {item.tags.slice(0, 3).map((tag: string, tagIndex: number) => (
                  <Badge
                    key={tagIndex}
                    variant="secondary"
                    className="text-xs px-2 py-0.5"
                  >
                    {tag}
                  </Badge>
                ))}
                {item.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    +{item.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}

            {/* Prompt 展开/收起区域 */}
            <div className="flex-1">
              {expandedPrompts.has(index) && (
                <div className="mb-3">
                  <div className="bg-muted/50 rounded-lg p-3 border border-border/50">
                    <p className="text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                      {item.prompt}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* 按钮区域 */}
            <div className="space-y-2">
              <Button
                onClick={() => togglePrompt(index)}
                variant="outline"
                size="sm"
                className="w-full transition-all duration-200"
              >
                {expandedPrompts.has(index) ? (
                  <>
                    <ChevronUp className="w-4 h-4 mr-2" />
                    隐藏 Prompt
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-4 h-4 mr-2" />
                    显示 Prompt
                  </>
                )}
              </Button>

              {expandedPrompts.has(index) && (
                <Button
                  onClick={() => handleCopyPrompt(item.prompt, index)}
                  variant="default"
                  size="sm"
                  className="w-full transition-all duration-200"
                >
                  {copiedIndex === index ? (
                    <>
                      <Check className="w-4 h-4 mr-2" />
                      已复制
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4 mr-2" />
                      复制 Prompt
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      );
    }
  };

  return (
    <section className="container py-16">
      <div className="mx-auto mb-12 text-center">
        <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
          {section.title}
        </h2>
        <p className="mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
          {section.description}
        </p>
      </div>

      {/* 网格布局 */}
      <div className={`grid gap-6 ${
        cardWidth === '50%'
          ? 'grid-cols-1 md:grid-cols-2'
          : cardWidth === '100%'
          ? 'grid-cols-1'
          : layout === 'horizontal'
          ? 'grid-cols-1 lg:grid-cols-2'
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      }`}>
        {section.items?.map((item, index) => renderCard(item, index))}
      </div>
    </section>
  );
}
